{% extends "base.html" %}

{% block title %}Admin Panel - Hostel Attendance 🏠{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="card-title mb-0">
                    <i class="fas fa-users-cog"></i> Student Management Panel 👥✨
                </h4>
            </div>
            <div class="card-body">
                <p class="lead">Manage your hostel squad! Add new members or remove old ones. 🎯</p>
            </div>
        </div>
    </div>
</div>

<!-- Add Student Form -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus"></i> Add New Student 🎉
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_student') }}" id="addStudentForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-user"></i> Full Name 📝
                        </label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="Enter awesome student name...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i> Email Address 📧
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required 
                               placeholder="<EMAIL>">
                        <div class="form-text">This email will be used for attendance check-in! 🎯</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="room_no" class="form-label">
                                    <i class="fas fa-door-open"></i> Room Number 🏠
                                </label>
                                <input type="text" class="form-control" id="room_no" name="room_no" required 
                                       placeholder="e.g., A-101">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="year" class="form-label">
                                    <i class="fas fa-graduation-cap"></i> Year 📚
                                </label>
                                <select class="form-select" id="year" name="year" required>
                                    <option value="">Select Year</option>
                                    <option value="1">1st Year 🌱</option>
                                    <option value="2">2nd Year 🌿</option>
                                    <option value="3">3rd Year 🌳</option>
                                    <option value="4">4th Year 🎓</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="branch" class="form-label">
                            <i class="fas fa-code-branch"></i> Branch/Department 🎓
                        </label>
                        <input type="text" class="form-control" id="branch" name="branch" required 
                               placeholder="e.g., Computer Science, Mechanical, etc.">
                    </div>
                    
                    <button type="submit" class="btn btn-success btn-lg w-100">
                        <i class="fas fa-plus-circle"></i> Add Student to Squad! 🚀
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Students List -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> Current Squad ({{ students|length }}) 👥
                </h5>
                <span class="badge bg-light text-dark">{{ students|length }} members</span>
            </div>
            <div class="card-body p-0">
                {% if students %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Student Info 📋</th>
                                    <th>Details 🏠</th>
                                    <th>Action 🗑️</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ student.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ student.email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <small>
                                            <i class="fas fa-door-open"></i> {{ student.room_no }}<br>
                                            <i class="fas fa-graduation-cap"></i> {{ student.branch }} - Year {{ student.year }}
                                        </small>
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('delete_student', student_id=student.id) }}" 
                                              onsubmit="return confirmDelete('{{ student.name }}')">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No students yet! 😅</h5>
                        <p class="text-muted">Add your first squad member to get started! 🚀</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> Squad Stats 📊
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-primary">{{ students|length }}</h3>
                            <p class="text-muted mb-0">Total Students 👥</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-success">{{ students|selectattr('year', 'equalto', 1)|list|length }}</h3>
                            <p class="text-muted mb-0">First Years 🌱</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-warning">{{ students|selectattr('year', 'equalto', 4)|list|length }}</h3>
                            <p class="text-muted mb-0">Final Years 🎓</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-info">{{ students|map(attribute='branch')|unique|list|length }}</h3>
                            <p class="text-muted mb-0">Branches 🎯</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(studentName) {
    return confirm(`Are you sure you want to remove ${studentName} from the squad? 🤔\n\nThis action cannot be undone! 😅`);
}

// Form validation
document.getElementById('addStudentForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address! 📧');
        return false;
    }
});
</script>
{% endblock %}
