#!/usr/bin/env python3
"""
QR Code Functionality Test Script
Tests all the QR code features of the Hostel Attendance System
"""

import requests
import json
from datetime import date, datetime

def test_qr_functionality():
    """Test all QR code functionality"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing QR Code Functionality...")
    print("=" * 50)
    
    # Test 1: Get QR Info
    print("\n1️⃣ Testing QR Info Endpoint...")
    try:
        response = requests.get(f"{base_url}/qr_info")
        if response.status_code == 200:
            qr_data = response.json()
            print("✅ QR Info endpoint working!")
            print(f"   Scan URL: {qr_data.get('scan_url')}")
            print(f"   Current Time: {qr_data.get('current_time')}")
            print(f"   Today: {qr_data.get('today')}")
            
            if qr_data.get('qr_info'):
                qr_info = qr_data['qr_info']
                print(f"   QR Valid: {qr_info.get('is_valid')}")
                print(f"   QR Date: {qr_info.get('date')}")
                print(f"   QR Hash: {qr_info.get('hash')}")
                
                scan_url = qr_data.get('scan_url')
                if scan_url:
                    print(f"\n2️⃣ Testing QR Scan URL...")
                    scan_response = requests.get(scan_url)
                    if scan_response.status_code == 200:
                        print("✅ QR Scan URL accessible!")
                        print(f"   URL: {scan_url}")
                    else:
                        print(f"❌ QR Scan URL failed: {scan_response.status_code}")
                else:
                    print("❌ No scan URL found")
            else:
                print("❌ No QR info found")
        else:
            print(f"❌ QR Info endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ QR Info test failed: {e}")
    
    # Test 3: Test QR Code Regeneration
    print(f"\n3️⃣ Testing QR Code Regeneration...")
    try:
        regen_response = requests.get(f"{base_url}/regenerate_qr")
        if regen_response.status_code == 200:
            print("✅ QR Code regeneration successful!")
        else:
            print(f"❌ QR Code regeneration failed: {regen_response.status_code}")
    except Exception as e:
        print(f"❌ QR regeneration test failed: {e}")
    
    # Test 4: Test Dashboard Access
    print(f"\n4️⃣ Testing Dashboard Access...")
    try:
        dashboard_response = requests.get(f"{base_url}/")
        if dashboard_response.status_code == 200:
            print("✅ Dashboard accessible!")
            if "QR Scan URL" in dashboard_response.text:
                print("✅ QR Scan URL visible on dashboard!")
            else:
                print("⚠️ QR Scan URL not visible on dashboard")
        else:
            print(f"❌ Dashboard failed: {dashboard_response.status_code}")
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
    
    # Test 5: Test Attendance Marking
    print(f"\n5️⃣ Testing Attendance Marking...")
    try:
        # Get fresh QR info
        qr_response = requests.get(f"{base_url}/qr_info")
        if qr_response.status_code == 200:
            qr_data = qr_response.json()
            qr_info = qr_data.get('qr_info')
            
            if qr_info:
                # Test attendance marking
                attendance_data = {
                    'full_name': 'Sarah Williams',
                    'password': '<EMAIL>',
                    'qr_date': qr_info['date'],
                    'qr_code': qr_info['hash']
                }
                
                mark_response = requests.post(
                    f"{base_url}/mark_attendance_scan",
                    data=attendance_data
                )
                
                if mark_response.status_code == 200:
                    result = mark_response.json()
                    if result.get('success'):
                        print("✅ Attendance marking successful!")
                        print(f"   Message: {result.get('message')}")
                        print(f"   Student: {result.get('student_name')}")
                        print(f"   Room: {result.get('room_no')}")
                        print(f"   Streak: {result.get('streak')}")
                    else:
                        print(f"⚠️ Attendance marking failed: {result.get('message')}")
                else:
                    print(f"❌ Attendance marking request failed: {mark_response.status_code}")
            else:
                print("❌ No QR info available for attendance test")
        else:
            print(f"❌ Could not get QR info for attendance test")
    except Exception as e:
        print(f"❌ Attendance marking test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 QR Code Functionality Test Complete!")
    print("\n📱 To test with a real smartphone:")
    print("1. Open your phone's camera app")
    print("2. Point it at the QR code on the dashboard")
    print("3. Tap the notification to open the attendance page")
    print("4. Fill in your name and email to mark attendance")
    print("\n🔗 Or copy this URL to test manually:")
    
    # Get final QR info
    try:
        final_response = requests.get(f"{base_url}/qr_info")
        if final_response.status_code == 200:
            final_data = final_response.json()
            scan_url = final_data.get('scan_url')
            if scan_url:
                print(f"   {scan_url}")
            else:
                print("   No scan URL available")
        else:
            print("   Could not retrieve final scan URL")
    except:
        print("   Error retrieving final scan URL")

if __name__ == "__main__":
    test_qr_functionality()
