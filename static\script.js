// Custom JavaScript for Hostel Attendance System

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Add fun animations to cards
    animateCards();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Add click effects to buttons
    addButtonEffects();
    
    // Auto-hide alerts after 5 seconds
    autoHideAlerts();
    
    // Add fun emoji reactions
    addEmojiReactions();
}

function animateCards() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function addButtonEffects() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

function autoHideAlerts() {
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);
    });
}

function addEmojiReactions() {
    // Add random emoji reactions to success messages
    const successElements = document.querySelectorAll('.alert-success, .text-success');
    const emojis = ['🎉', '✨', '🚀', '🎯', '💫', '⭐', '🔥', '👏'];
    
    successElements.forEach(element => {
        const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
        if (!element.textContent.includes('🎉') && !element.textContent.includes('✨')) {
            element.textContent += ` ${randomEmoji}`;
        }
    });
}

// QR Code related functions
function refreshQRCode() {
    const qrImages = document.querySelectorAll('.qr-code-img');
    qrImages.forEach(img => {
        const currentSrc = img.src;
        const newSrc = currentSrc.split('?')[0] + '?t=' + new Date().getTime();
        img.src = newSrc;
        
        // Add refresh animation
        img.style.transform = 'rotate(360deg)';
        setTimeout(() => {
            img.style.transform = 'rotate(0deg)';
        }, 500);
    });
    
    showNotification('QR Code refreshed! 🔄', 'info');
}

// Notification system
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getIconForType(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, duration);
}

function getIconForType(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle',
        'danger': 'times-circle'
    };
    return icons[type] || 'info-circle';
}

// Form validation helpers
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        }
        
        // Special validation for email
        if (input.type === 'email' && input.value.trim()) {
            if (!validateEmail(input.value)) {
                input.classList.add('is-invalid');
                isValid = false;
            }
        }
    });
    
    return isValid;
}

// Fun loading messages
const loadingMessages = [
    'Checking those hostel vibes... 🏠',
    'Validating your awesomeness... ✨',
    'Processing your check-in... 🎯',
    'Almost there, champ! 🚀',
    'Making magic happen... 🪄'
];

function showLoadingMessage() {
    const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
    return randomMessage;
}

// Streak celebration
function celebrateStreak(days) {
    if (days >= 7) {
        createConfetti();
        showNotification(`🔥 AMAZING! ${days} days streak! You're on fire! 🔥`, 'success', 5000);
    } else if (days >= 3) {
        showNotification(`⭐ Great job! ${days} days streak! Keep it up! ⭐`, 'success', 4000);
    }
}

function createConfetti() {
    // Simple confetti effect
    const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
    
    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.cssText = `
                position: fixed;
                top: -10px;
                left: ${Math.random() * 100}%;
                width: 10px;
                height: 10px;
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                z-index: 9999;
                animation: confettiFall 3s linear forwards;
                pointer-events: none;
            `;
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 50);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes confettiFall {
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: rippleEffect 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes rippleEffect {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .is-invalid {
        border-color: #dc3545 !important;
        animation: shake 0.5s ease-in-out;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;

document.head.appendChild(style);

// Auto-refresh functionality
function startAutoRefresh() {
    // Refresh QR code every 5 minutes
    setInterval(() => {
        refreshQRCode();
    }, 300000);
}

// Initialize auto-refresh on pages with QR codes
if (document.querySelector('.qr-code-img')) {
    startAutoRefresh();
}

// Fun console message
console.log(`
🎉 Welcome to the Hostel Attendance System! 🏠
Made with ❤️ and lots of coffee ☕
Keep those attendance streaks going! 🔥
`);

// Export functions for global use
window.HostelApp = {
    refreshQRCode,
    showNotification,
    validateForm,
    celebrateStreak,
    showLoadingMessage
};
