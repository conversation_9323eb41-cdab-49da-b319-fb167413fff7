import qrcode
import hashlib
import os
from datetime import date, datetime
import sqlite3
from config import Config

class QRCodeManager:
    def __init__(self):
        self.qr_dir = Config.QR_CODE_DIR
        self.db_path = Config.DATABASE_PATH
        
    def generate_daily_qr_code(self):
        """Generate a unique QR code for today"""
        today = date.today()
        
        # Check if QR code already exists for today
        if self.qr_code_exists_for_date(today):
            return self.get_qr_code_path(today)
        
        # Generate unique code based on date and secret
        date_string = today.strftime('%Y-%m-%d')
        secret_data = f"{date_string}_{Config.SECRET_KEY}_hostel_attendance"

        # Create hash for the QR code content
        code_hash = hashlib.sha256(secret_data.encode()).hexdigest()[:16]

        # Create URL that opens the attendance page directly
        qr_content = f"http://localhost:5000/scan_attendance?date={date_string}&code={code_hash}"
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=Config.QR_CODE_SIZE,
            border=Config.QR_CODE_BORDER,
        )
        
        qr.add_data(qr_content)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Save QR code
        filename = f"qr_{today.strftime('%Y_%m_%d')}.png"
        filepath = os.path.join(self.qr_dir, filename)
        img.save(filepath)
        
        # Store in database
        self.store_qr_code_in_db(today, code_hash)
        
        return filepath
    
    def qr_code_exists_for_date(self, check_date):
        """Check if QR code exists for given date"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM qr_codes WHERE date = ?
        ''', (check_date,))
        
        result = cursor.fetchone()
        conn.close()
        
        return result is not None
    
    def store_qr_code_in_db(self, qr_date, code_hash):
        """Store QR code info in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO qr_codes (date, code_hash)
            VALUES (?, ?)
        ''', (qr_date, code_hash))
        
        conn.commit()
        conn.close()
    
    def get_qr_code_path(self, qr_date):
        """Get QR code file path for given date"""
        filename = f"qr_{qr_date.strftime('%Y_%m_%d')}.png"
        return os.path.join(self.qr_dir, filename)
    
    def validate_qr_code(self, qr_date_str, qr_hash):
        """Validate if QR code is valid for today"""
        try:
            # Check if it's today's QR code
            qr_date = datetime.strptime(qr_date_str, '%Y-%m-%d').date()
            today = date.today()

            if qr_date != today:
                return False

            # Verify hash
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT code_hash FROM qr_codes WHERE date = ?
            ''', (today,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0] == qr_hash:
                return True

            return False

        except Exception:
            return False
    
    def get_today_qr_url(self):
        """Get today's QR code URL for web display"""
        today = date.today()
        filename = f"qr_{today.strftime('%Y_%m_%d')}.png"
        return f"/static/qr_codes/{filename}"
    
    def cleanup_old_qr_codes(self, days_to_keep=7):
        """Clean up QR codes older than specified days"""
        try:
            # Get all QR code files
            for filename in os.listdir(self.qr_dir):
                if filename.startswith('qr_') and filename.endswith('.png'):
                    filepath = os.path.join(self.qr_dir, filename)
                    
                    # Get file creation time
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    days_old = (datetime.now() - file_time).days
                    
                    if days_old > days_to_keep:
                        os.remove(filepath)
            
            # Clean up database entries
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            from datetime import timedelta
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            cursor.execute('''
                DELETE FROM qr_codes WHERE date < ?
            ''', (cutoff_date,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error cleaning up old QR codes: {e}")
