import qrcode
import hashlib
import os
from datetime import date, datetime, time, timedelta
import sqlite3
import schedule
import threading
import pytz
from config import Config

class QRCodeManager:
    def __init__(self):
        self.qr_dir = Config.QR_CODE_DIR
        self.db_path = Config.DATABASE_PATH
        self.base_url = "http://localhost:5000"  # Make this configurable
        self._start_scheduler()

    def _start_scheduler(self):
        """Start the background scheduler for automatic QR code refresh"""
        def run_scheduler():
            schedule.every().day.at("00:00").do(self._midnight_refresh)
            while True:
                schedule.run_pending()
                import time
                time.sleep(60)  # Check every minute

        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()

    def _midnight_refresh(self):
        """Refresh QR codes at midnight"""
        print("🕛 Midnight QR code refresh triggered...")
        self.cleanup_old_qr_codes(days_to_keep=1)
        self.generate_daily_qr_code()
        print("✅ QR code refreshed for new day!")

    def generate_daily_qr_code(self, force_regenerate=False):
        """Generate a unique QR code for today with 24-hour validity"""
        today = date.today()

        # Check if QR code already exists for today (unless forcing regeneration)
        if not force_regenerate and self.qr_code_exists_for_date(today):
            print(f"📱 QR code already exists for {today}")
            return self.get_qr_code_path(today)

        # Generate unique code based on date, time, and secret for better security
        date_string = today.strftime('%Y-%m-%d')
        timestamp = datetime.now().strftime('%H%M%S')
        secret_data = f"{date_string}_{timestamp}_{Config.SECRET_KEY}_hostel_attendance_24h"

        # Create hash for the QR code content
        code_hash = hashlib.sha256(secret_data.encode()).hexdigest()[:16]

        # Create URL that opens the attendance page directly
        qr_content = f"{self.base_url}/scan_attendance?date={date_string}&code={code_hash}"

        print(f"🔗 Generating QR code with URL: {qr_content}")

        # Generate QR code with higher error correction for better scanning
        qr = qrcode.QRCode(
            version=2,  # Increased version for longer URLs
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Medium error correction
            box_size=Config.QR_CODE_SIZE,
            border=Config.QR_CODE_BORDER,
        )

        qr.add_data(qr_content)
        qr.make(fit=True)

        # Create QR code image with better contrast
        img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code
        filename = f"qr_{today.strftime('%Y_%m_%d')}.png"
        filepath = os.path.join(self.qr_dir, filename)
        img.save(filepath)

        # Store in database with 24-hour validity
        self.store_qr_code_in_db(today, code_hash, qr_content)

        print(f"✅ QR code generated and saved: {filepath}")
        return filepath
    
    def qr_code_exists_for_date(self, check_date):
        """Check if QR code exists for given date"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM qr_codes WHERE date = ?
        ''', (check_date,))
        
        result = cursor.fetchone()
        conn.close()
        
        return result is not None
    
    def store_qr_code_in_db(self, qr_date, code_hash, qr_url):
        """Store QR code info in database with full URL"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # First, ensure the table has the url column
        try:
            cursor.execute('ALTER TABLE qr_codes ADD COLUMN url TEXT')
        except sqlite3.OperationalError:
            pass  # Column already exists

        cursor.execute('''
            INSERT OR REPLACE INTO qr_codes (date, code_hash, url, created_at)
            VALUES (?, ?, ?, ?)
        ''', (qr_date, code_hash, qr_url, datetime.now()))

        conn.commit()
        conn.close()
        print(f"💾 Stored QR code in database: {qr_date} -> {qr_url}")
    
    def get_qr_code_path(self, qr_date):
        """Get QR code file path for given date"""
        filename = f"qr_{qr_date.strftime('%Y_%m_%d')}.png"
        return os.path.join(self.qr_dir, filename)
    
    def validate_qr_code(self, qr_date_str, qr_hash):
        """Validate if QR code is valid with 24-hour validity check"""
        try:
            # Parse the QR code date
            qr_date = datetime.strptime(qr_date_str, '%Y-%m-%d').date()
            today = date.today()

            # Check if QR code is for today (24-hour validity)
            if qr_date != today:
                print(f"❌ QR code date mismatch: {qr_date} vs {today}")
                return False

            # Get current time for 24-hour validity check
            now = datetime.now()
            qr_start_time = datetime.combine(qr_date, datetime.min.time())  # 00:00:00
            qr_end_time = datetime.combine(qr_date, datetime.max.time())    # 23:59:59

            # Check if current time is within 24-hour validity window
            if not (qr_start_time <= now <= qr_end_time):
                print(f"❌ QR code outside 24-hour validity window")
                return False

            # Verify hash against database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT code_hash, url, created_at FROM qr_codes WHERE date = ?
            ''', (today,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0] == qr_hash:
                print(f"✅ QR code validated successfully for {today}")
                return True

            print(f"❌ QR code hash mismatch or not found")
            return False

        except Exception as e:
            print(f"❌ QR code validation error: {e}")
            return False
    
    def get_today_qr_url(self):
        """Get today's QR code URL for web display"""
        today = date.today()
        filename = f"qr_{today.strftime('%Y_%m_%d')}.png"
        return f"/static/qr_codes/{filename}"

    def get_today_scan_url(self):
        """Get today's QR scan URL that the QR code contains - persistent throughout the day"""
        today = date.today()

        # Get the URL from database (should persist all day)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT code_hash, url FROM qr_codes WHERE date = ?
        ''', (today,))

        result = cursor.fetchone()
        conn.close()

        if result:
            code_hash = result[0]
            stored_url = result[1]

            # Return stored URL if available, otherwise construct it
            if stored_url:
                print(f"📱 Retrieved persistent QR URL: {stored_url}")
                return stored_url
            else:
                # Fallback: construct URL from hash
                date_string = today.strftime('%Y-%m-%d')
                constructed_url = f"{self.base_url}/scan_attendance?date={date_string}&code={code_hash}"
                print(f"🔧 Constructed QR URL: {constructed_url}")
                return constructed_url

        # If no QR code exists for today, generate one
        print(f"⚠️ No QR code found for {today}, generating new one...")
        self.generate_daily_qr_code()
        return self.get_today_scan_url()  # Recursive call after generation

    def get_qr_code_info(self):
        """Get complete QR code information for today"""
        today = date.today()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT date, code_hash, url, created_at FROM qr_codes WHERE date = ?
        ''', (today,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'date': result[0],
                'hash': result[1],
                'url': result[2],
                'created_at': result[3],
                'is_valid': self.validate_qr_code(result[0], result[1])
            }

        return None
    
    def cleanup_old_qr_codes(self, days_to_keep=7):
        """Clean up QR codes older than specified days"""
        try:
            # Get all QR code files
            for filename in os.listdir(self.qr_dir):
                if filename.startswith('qr_') and filename.endswith('.png'):
                    filepath = os.path.join(self.qr_dir, filename)
                    
                    # Get file creation time
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    days_old = (datetime.now() - file_time).days
                    
                    if days_old > days_to_keep:
                        os.remove(filepath)
            
            # Clean up database entries
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            from datetime import timedelta
            cutoff_date = date.today() - timedelta(days=days_to_keep)
            cursor.execute('''
                DELETE FROM qr_codes WHERE date < ?
            ''', (cutoff_date,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error cleaning up old QR codes: {e}")
