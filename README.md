# 🏠 Hostel Attendance System ✨

A fun and interactive Python-based hostel attendance system with QR code functionality! Built with Flask and lots of hostel vibes! 🎉

## 🌟 Features

### 👥 User Management
- **Add Students**: Register new hostel members with Name, Email, Room No., Branch, and Year
- **Delete Students**: Remove students from the system
- **View All Students**: See complete list of registered hostel members
- **Email-based Authentication**: Simple email verification for attendance

### 📱 QR Code Attendance
- **Daily QR Codes**: Unique QR codes generated fresh every day
- **Secure Validation**: QR codes expire daily for security
- **Mobile Friendly**: Scan with any smartphone camera
- **Demo Mode**: Simulate QR scanning for testing

### 🎯 Fun & Interactive Experience
- **Trendy Messages**: Funny, engaging messages throughout the app
- **Emoji Everywhere**: Popular emojis to keep things fun
- **Streak Tracking**: Build attendance streaks for motivation
- **Success Celebrations**: Special animations for achievements
- **Responsive Design**: Works perfectly on mobile and desktop

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Up Demo Data (Optional)
```bash
python demo_setup.py
```

### 3. Run the Application
```bash
python app.py
```

### 4. Open Your Browser
Visit: `http://localhost:5000`

## 📱 How to Use

### For Students:
1. **Get Registered**: Ask admin to add you with your email
2. **Scan QR Code**: Use your phone camera to scan today's QR code
3. **Enter Email**: Type your registered email address
4. **Check In**: Hit the button and you're done! 🎉
5. **Build Streaks**: Check in daily to build your attendance streak! 🔥

### For Admins:
1. **Add Students**: Go to Admin panel and add new students
2. **Manage Users**: View all students and remove if needed
3. **Monitor Attendance**: QR codes refresh daily automatically

## 🎮 Demo Accounts

After running `demo_setup.py`, you can test with these emails:
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

## 🛠 Technical Details

### Tech Stack:
- **Backend**: Flask (Python)
- **Database**: SQLite
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **QR Codes**: qrcode library with Pillow
- **Icons**: Font Awesome

### File Structure:
```
hostel-attendance/
├── app.py              # Main Flask application
├── models.py           # Database models and operations
├── qr_generator.py     # QR code generation and validation
├── config.py           # Configuration settings
├── demo_setup.py       # Demo data setup script
├── requirements.txt    # Python dependencies
├── templates/          # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── admin.html
│   └── attendance.html
├── static/             # CSS, JS, and QR codes
│   ├── style.css
│   ├── script.js
│   └── qr_codes/       # Generated QR codes
└── hostel_attendance.db # SQLite database (auto-created)
```

## 🎨 Customization

### Fun Messages:
Edit `config.py` to customize:
- Welcome messages
- Success messages
- Streak messages
- Error messages

### Styling:
Modify `static/style.css` for:
- Colors and themes
- Animations
- Layout adjustments

### QR Code Settings:
Adjust in `config.py`:
- QR code size
- Border thickness
- Expiration time

## 🔧 Configuration

Key settings in `config.py`:
- `DATABASE_PATH`: SQLite database location
- `QR_CODE_DIR`: QR code storage directory
- `SECRET_KEY`: Application secret key
- `DEBUG`: Debug mode toggle

## 📊 Database Schema

### Students Table:
- id (Primary Key)
- name
- email (Unique)
- room_no
- branch
- year
- created_at

### Attendance Table:
- id (Primary Key)
- student_id (Foreign Key)
- date
- check_in_time

### QR Codes Table:
- id (Primary Key)
- date (Unique)
- code_hash
- created_at

## 🎯 API Endpoints

- `GET /` - Main dashboard
- `GET /admin` - Admin panel
- `POST /add_student` - Add new student
- `POST /delete_student/<id>` - Delete student
- `GET /attendance` - QR scan page
- `POST /check_in` - Process attendance
- `POST /api/validate_qr` - Validate QR code

## 🚨 Security Features

- **Daily QR Expiration**: QR codes are valid only for the current day
- **Hash Validation**: QR codes use secure hashing
- **Email Verification**: Only registered emails can check in
- **Duplicate Prevention**: Can't check in twice on the same day

## 🎉 Fun Features

- **Streak Tracking**: See how many consecutive days you've attended
- **Random Messages**: Different fun messages each time
- **Confetti Animation**: Special effects for long streaks
- **Mobile Optimized**: Perfect for smartphone use
- **Auto-refresh**: QR codes update automatically

## 🤝 Contributing

Feel free to contribute to make this even more awesome! 🚀

## 📝 License

Made with ❤️ for the coolest hostels ever! 🏠✨

---

**Ready to track those hostel vibes? Let's go! 🎉🚀**
