{% extends "base.html" %}

{% block title %}Dashboard - Hostel Attendance 🏠{% endblock %}

{% block content %}
<div class="row">
    <!-- Welcome Section -->
    <div class="col-12">
        <div class="card bg-gradient-primary text-white mb-4">
            <div class="card-body text-center">
                <h1 class="card-title">{{ welcome_message }}</h1>
                <p class="card-text fs-5">{{ today_date }}</p>
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-users fa-2x me-2"></i>
                            <div>
                                <h3 class="mb-0">{{ total_students }}</h3>
                                <small>Total Students</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-user-check fa-2x me-2"></i>
                            <div>
                                <h3 class="mb-0">{{ attendance_count }}</h3>
                                <small>Present Today</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-percentage fa-2x me-2"></i>
                            <div>
                                <h3 class="mb-0">{{ attendance_percentage }}%</h3>
                                <small>Attendance Rate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-qrcode fa-2x me-2"></i>
                            <div>
                                <h3 class="mb-0">Fresh</h3>
                                <small>QR Code Ready</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- QR Code Section -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-qrcode"></i> Today's QR Code 📱
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="qr-container mb-3">
                    <img src="{{ qr_url }}" alt="Today's QR Code" class="img-fluid qr-code-img">
                </div>
                <h6 class="text-success">📱 Scan to open attendance page automatically! 🎯</h6>
                <p class="small text-muted">
                    This QR code opens the attendance form directly on your device! ⏰
                </p>

                {% if qr_scan_url %}
                <div class="alert alert-info mb-3">
                    <small>
                        <i class="fas fa-link"></i> QR Code URL:
                        <a href="{{ qr_scan_url }}" target="_blank" class="text-decoration-none">
                            {{ qr_scan_url }}
                        </a>
                    </small>
                </div>
                {% endif %}

                <div class="d-grid gap-2">
                    {% if qr_scan_url %}
                    <a href="{{ qr_scan_url }}" class="btn btn-success btn-lg" target="_blank">
                        <i class="fas fa-external-link-alt"></i> Open Attendance Page 🚀
                    </a>
                    {% endif %}
                    <a href="{{ url_for('attendance_page') }}" class="btn btn-outline-success">
                        <i class="fas fa-mobile-alt"></i> Legacy Check In 📱
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions ⚡
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('attendance_page') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-qrcode"></i> Mark Attendance 📝
                    </a>
                    <a href="{{ url_for('admin') }}" class="btn btn-warning btn-lg">
                        <i class="fas fa-users-cog"></i> Manage Students 👥
                    </a>
                    <button class="btn btn-info btn-lg" onclick="refreshQR()">
                        <i class="fas fa-sync-alt"></i> Refresh QR Code 🔄
                    </button>
                </div>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-primary">
                        <i class="fas fa-lightbulb"></i> Pro Tips! 💡
                    </h6>
                    <ul class="small mb-0">
                        <li>QR codes refresh daily for security 🔒</li>
                        <li>Build your attendance streak! 🔥</li>
                        <li>Check in early to avoid rush! ⏰</li>
                        <li>Make sure your email is registered! 📧</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Attendance Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check"></i> Today's Attendance ({{ attendance_count }}/{{ total_students }}) 📋
                </h5>
                <span class="badge bg-light text-dark fs-6">{{ attendance_percentage }}% Present</span>
            </div>
            <div class="card-body">
                {% if today_attendance %}
                    <div class="row">
                        {% for student in today_attendance %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light border-success">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-user-check fa-2x text-success"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1">{{ student.name }}</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-door-open"></i> {{ student.room_no }} |
                                                <i class="fas fa-graduation-cap"></i> {{ student.branch }}
                                            </small>
                                            <br>
                                            <small class="text-success">
                                                <i class="fas fa-clock"></i>
                                                {% set check_in_time = student.check_in_time %}
                                                {% if check_in_time %}
                                                    {{ check_in_time.split(' ')[1][:5] if ' ' in check_in_time else check_in_time[:5] }}
                                                {% else %}
                                                    Just now
                                                {% endif %}
                                            </small>
                                        </div>
                                        <div>
                                            <span class="badge bg-success">✓</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    {% if attendance_count < total_students %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>{{ total_students - attendance_count }} students</strong> haven't checked in yet today!
                        Share the QR code to help them mark their attendance! 📱
                    </div>
                    {% endif %}

                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No one has checked in yet today! 😅</h5>
                        <p class="text-muted">Be the first to mark your attendance and start the hostel vibes! 🚀</p>
                        {% if qr_scan_url %}
                        <a href="{{ qr_scan_url }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-qrcode"></i> Start Attendance Now! 🎯
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Fun Stats Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> Hostel Vibes Status 📊
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="fas fa-fire fa-3x text-danger mb-2"></i>
                            <h4>🔥 Streaks</h4>
                            <p class="text-muted">Keep that momentum!</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="fas fa-trophy fa-3x text-warning mb-2"></i>
                            <h4>🏆 Champions</h4>
                            <p class="text-muted">Perfect attendance!</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="fas fa-star fa-3x text-success mb-2"></i>
                            <h4>⭐ Stars</h4>
                            <p class="text-muted">Consistent check-ins!</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="fas fa-rocket fa-3x text-primary mb-2"></i>
                            <h4>🚀 Rockstars</h4>
                            <p class="text-muted">Never miss a day!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshQR() {
    location.reload();
}

// Auto-refresh QR code every 5 minutes
setInterval(function() {
    const qrImg = document.querySelector('.qr-code-img');
    if (qrImg) {
        qrImg.src = qrImg.src + '?t=' + new Date().getTime();
    }
}, 300000); // 5 minutes
</script>
{% endblock %}
