import os
from datetime import datetime

class Config:
    # Database configuration
    DATABASE_PATH = 'hostel_attendance.db'
    
    # QR Code configuration
    QR_CODE_DIR = 'static/qr_codes'
    QR_CODE_SIZE = 10
    QR_CODE_BORDER = 4
    
    # Application settings
    SECRET_KEY = 'hostel_vibes_2024_🎉'
    DEBUG = True
    
    # Fun messages and emojis
    WELCOME_MESSAGES = [
        "Welcome to the coolest hostel attendance system! 🏠✨",
        "Ready to mark your presence? Let's go! 🚀",
        "Time to check in and show those hostel vibes! 🎉",
        "Another day, another check-in! You're awesome! 😎"
    ]
    
    SUCCESS_MESSAGES = [
        "Boom! 💥 You're checked in! Hostel vibes: ON! 🎉😎",
        "Yass! ✨ Present and accounted for! You're killing it! 🔥",
        "Check-in complete! 🎯 You're officially part of today's squad! 👥",
        "Attendance marked! 📝 Keep that streak going, champ! 🏆",
        "You're here! 🙌 The hostel just got 10x cooler! 😎✨"
    ]
    
    STREAK_MESSAGES = [
        "🔥 You're on fire! {days} days streak! Keep it up!",
        "🏆 Consistency king/queen! {days} days and counting!",
        "⚡ Unstoppable! {days} days of perfect attendance!",
        "🌟 Superstar alert! {days} days streak achieved!"
    ]
    
    ERROR_MESSAGES = [
        "Oops! 😅 Something went wrong. Try again!",
        "Hmm... 🤔 That didn't work. Give it another shot!",
        "Error alert! 🚨 But don't worry, we'll fix this!",
        "Technical hiccup! 💻 Please try once more!"
    ]

# Create directories if they don't exist
os.makedirs(Config.QR_CODE_DIR, exist_ok=True)
