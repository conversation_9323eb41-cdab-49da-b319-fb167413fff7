{% extends "base.html" %}

{% block title %}Check In - Hostel Attendance 🏠{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-info text-white text-center">
                <h4 class="card-title mb-0">
                    <i class="fas fa-arrow-left"></i> Redirecting to Modern QR System 🚀
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="alert alert-primary">
                    <i class="fas fa-info-circle"></i>
                    <strong>Modern QR System Available!</strong> QR codes now open attendance pages automatically!
                    <a href="{{ url_for('index') }}" class="alert-link">Click here to get the new QR code</a>.
                </div>
                <div class="mb-4">
                    <i class="fas fa-qrcode fa-4x text-primary mb-3"></i>
                    <h5 class="text-primary">Use QR Code for Best Experience! 📱</h5>
                    <p class="text-muted">Scan QR codes directly with your smartphone camera for instant attendance marking! 🎯</p>
                </div>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home"></i> Go to QR Dashboard 🏠
                    </a>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleLegacySection()">
                        <i class="fas fa-cog"></i> Show Legacy Method (Testing Only)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Legacy form section - hidden by default -->
<div class="row justify-content-center mt-4" style="display: none;" id="legacySection">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-secondary text-white text-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Legacy Method (For Testing Only) 🔧
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> This is the old method. Please use QR codes for the best experience!
                </div>

                <!-- QR Code Display -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header text-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-qrcode"></i> Today's QR Code 📱
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <img src="{{ qr_url }}" alt="Today's QR Code" class="img-fluid qr-code-img mb-3">
                                <p class="small text-muted">
                                    <i class="fas fa-clock"></i> Valid only for today! ⏰
                                </p>
                                <button class="btn btn-outline-primary btn-sm" onclick="simulateQRScan()">
                                    <i class="fas fa-camera"></i> Simulate QR Scan 📸
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Check-in Form -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-check"></i> Enter Your Details 📝
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="checkInForm">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i> Your Email Address 📧
                                        </label>
                                        <input type="email" class="form-control form-control-lg" 
                                               id="email" name="email" required 
                                               placeholder="Enter your registered email...">
                                        <div class="form-text">
                                            Make sure this matches your registered email! 🎯
                                        </div>
                                    </div>

                                    <input type="hidden" id="qrContent" name="qr_content" value="">

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success btn-lg" id="checkInBtn">
                                            <i class="fas fa-check-circle"></i> Check Me In! 🎉
                                        </button>
                                    </div>
                                </form>

                                <!-- Status Messages -->
                                <div id="statusMessage" class="mt-3" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle"></i> How to Check In 📋
                                </h6>
                                <ol class="mb-0">
                                    <li>Scan the QR code with your phone camera 📱</li>
                                    <li>Or click "Simulate QR Scan" for demo 🎭</li>
                                    <li>Enter your registered email address 📧</li>
                                    <li>Hit "Check Me In!" and you're done! 🎉</li>
                                </ol>
                                <div class="mt-3">
                                    <small>
                                        <i class="fas fa-lightbulb"></i> 
                                        <strong>Pro Tip:</strong> Build your attendance streak for extra hostel cred! 🔥
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Check-in Successful! 🎉
                </h5>
            </div>
            <div class="modal-body text-center">
                <div id="successContent">
                    <!-- Success message will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                    Awesome! 😎
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Simulate QR code scanning for demo purposes
function simulateQRScan() {
    const today = new Date().toISOString().split('T')[0];
    const qrContent = `HOSTEL_ATTENDANCE_${today}_demo123`;
    document.getElementById('qrContent').value = qrContent;
    
    // Show visual feedback
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> QR Scanned! ✅';
    btn.classList.remove('btn-outline-primary');
    btn.classList.add('btn-success');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-primary');
    }, 2000);
    
    showStatus('QR code scanned successfully! 📱 Now enter your email! 🎯', 'success');
}

// Handle form submission
document.getElementById('checkInForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    const qrContent = document.getElementById('qrContent').value;
    
    if (!email) {
        showStatus('Please enter your email address! 📧', 'error');
        return;
    }
    
    if (!qrContent) {
        showStatus('Please scan the QR code first! 📱', 'error');
        return;
    }
    
    // Show loading state
    const btn = document.getElementById('checkInBtn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking you in... ⏳';
    btn.disabled = true;
    
    // Submit check-in
    fetch('/check_in', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `email=${encodeURIComponent(email)}&qr_content=${encodeURIComponent(qrContent)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessModal(data);
        } else {
            showStatus(data.message, 'error');
        }
    })
    .catch(error => {
        showStatus('Something went wrong! Please try again! 😅', 'error');
    })
    .finally(() => {
        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
});

function showStatus(message, type) {
    const statusDiv = document.getElementById('statusMessage');
    statusDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
    statusDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    statusDiv.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 5000);
}

function showSuccessModal(data) {
    const successContent = document.getElementById('successContent');
    successContent.innerHTML = `
        <div class="mb-3">
            <i class="fas fa-user-check fa-3x text-success mb-3"></i>
            <h4>Welcome, ${data.student_name}! 👋</h4>
        </div>
        <div class="alert alert-success">
            ${data.message}
        </div>
        <div class="row text-center">
            <div class="col-6">
                <h5 class="text-primary">${data.room_no}</h5>
                <small class="text-muted">Room Number 🏠</small>
            </div>
            <div class="col-6">
                <h5 class="text-warning">${data.streak}</h5>
                <small class="text-muted">Day Streak 🔥</small>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();
    
    // Reset form
    document.getElementById('checkInForm').reset();
    document.getElementById('qrContent').value = '';
}

// Auto-focus email input (if it exists)
const emailInput = document.getElementById('email');
if (emailInput) {
    emailInput.focus();
}

// Toggle legacy section visibility
function toggleLegacySection() {
    const legacySection = document.getElementById('legacySection');
    const toggleBtn = event.target;

    if (legacySection.style.display === 'none' || legacySection.style.display === '') {
        legacySection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Legacy Method';
        toggleBtn.classList.remove('btn-outline-secondary');
        toggleBtn.classList.add('btn-secondary');

        // Scroll to legacy section
        legacySection.scrollIntoView({ behavior: 'smooth' });

        // Focus email input if available
        setTimeout(() => {
            const legacyEmailInput = document.getElementById('email');
            if (legacyEmailInput) {
                legacyEmailInput.focus();
            }
        }, 500);
    } else {
        legacySection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-cog"></i> Show Legacy Method (Testing Only)';
        toggleBtn.classList.remove('btn-secondary');
        toggleBtn.classList.add('btn-outline-secondary');
    }
}
</script>
{% endblock %}
