#!/usr/bin/env python3
"""
PDF Export Test Script
Tests the PDF generation functionality
"""

from models import DatabaseManager
from pdf_generator import AttendancePDFGenerator
from datetime import date
import os

def test_pdf_export():
    """Test PDF export functionality"""
    print("📄 Testing PDF Export Functionality...")
    print("=" * 50)
    
    # Initialize managers
    db_manager = DatabaseManager()
    pdf_generator = AttendancePDFGenerator()
    
    # Get current attendance data
    print("\n1️⃣ Getting Attendance Data...")
    today_attendance = db_manager.get_today_attendance()
    total_students = len(db_manager.get_all_students())
    attendance_count = len(today_attendance)
    attendance_percentage = round((attendance_count / total_students * 100) if total_students > 0 else 0, 1)
    
    print(f"   📊 Total Students: {total_students}")
    print(f"   ✅ Present Today: {attendance_count}")
    print(f"   📈 Attendance Rate: {attendance_percentage}%")
    
    if attendance_count == 0:
        print("\n   📝 No attendance found - marking some students for testing...")
        
        # Mark attendance for testing
        test_students = [
            ('<PERSON><PERSON>', '<EMAIL>'),
            ('<PERSON>', '<EMAIL>'),
            ('<PERSON>', '<EMAIL>')
        ]
        
        for name, email in test_students:
            student = db_manager.validate_student_credentials(name, email)
            if student and not db_manager.is_present_today(student['id']):
                db_manager.mark_attendance(student['id'])
                print(f"   ✅ Marked attendance for {name}")
        
        # Refresh data
        today_attendance = db_manager.get_today_attendance()
        attendance_count = len(today_attendance)
        attendance_percentage = round((attendance_count / total_students * 100) if total_students > 0 else 0, 1)
        
        print(f"   📊 Updated attendance count: {attendance_count}")
    
    # Test PDF generation
    print(f"\n2️⃣ Testing PDF Generation...")
    
    try:
        pdf_data = pdf_generator.generate_attendance_pdf(
            attendance_data=today_attendance,
            total_students=total_students,
            attendance_count=attendance_count,
            attendance_percentage=attendance_percentage,
            report_date=date.today()
        )
        
        print(f"   ✅ PDF generated successfully!")
        print(f"   📄 PDF size: {len(pdf_data)} bytes")
        
        # Save test PDF
        filename = f"test_attendance_report_{date.today().strftime('%Y-%m-%d')}.pdf"
        with open(filename, 'wb') as f:
            f.write(pdf_data)
        
        print(f"   💾 Test PDF saved as: {filename}")
        
        # Check if file exists and has content
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            print(f"   ✅ PDF file created successfully!")
            print(f"   📁 File size: {os.path.getsize(filename)} bytes")
        else:
            print(f"   ❌ PDF file creation failed!")
        
    except Exception as e:
        print(f"   ❌ PDF generation failed: {e}")
        return False
    
    # Test PDF content structure
    print(f"\n3️⃣ Testing PDF Content Structure...")
    
    print(f"   📋 Attendance data structure:")
    for i, student in enumerate(today_attendance[:3], 1):  # Show first 3 students
        print(f"      {i}. {student['name']} ({student['room_no']}) - {student['branch']} Year {student['year']}")
        print(f"         Check-in: {student['check_in_time']}")
    
    if len(today_attendance) > 3:
        print(f"      ... and {len(today_attendance) - 3} more students")
    
    print(f"\n4️⃣ Testing Web Endpoint...")
    
    try:
        import requests
        
        # Test the web endpoint
        response = requests.get('http://localhost:5000/admin/export_attendance_pdf')
        
        if response.status_code == 200:
            print(f"   ✅ Web endpoint working!")
            print(f"   📄 Response size: {len(response.content)} bytes")
            print(f"   📋 Content-Type: {response.headers.get('Content-Type', 'Not set')}")
            print(f"   📁 Content-Disposition: {response.headers.get('Content-Disposition', 'Not set')}")
            
            # Save web response PDF
            web_filename = f"web_test_attendance_report_{date.today().strftime('%Y-%m-%d')}.pdf"
            with open(web_filename, 'wb') as f:
                f.write(response.content)
            print(f"   💾 Web PDF saved as: {web_filename}")
            
        else:
            print(f"   ❌ Web endpoint failed: {response.status_code}")
            print(f"   📄 Response: {response.text[:200]}...")
    
    except ImportError:
        print(f"   ⚠️ Requests library not available - skipping web endpoint test")
    except Exception as e:
        print(f"   ❌ Web endpoint test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 PDF Export Test Complete!")
    print("\n📱 To test in browser:")
    print("1. Visit http://localhost:5000")
    print("2. Look for 'Export to PDF' button in Today's Attendance section")
    print("3. Click the button to download the PDF report")
    print("4. Check the downloaded file for proper formatting")
    
    print(f"\n📄 PDF Features Included:")
    print("✅ Professional header with hostel branding")
    print("✅ Date and generation timestamp")
    print("✅ Summary statistics table")
    print("✅ Detailed attendance list with all student info")
    print("✅ Proper formatting and styling")
    print("✅ Footer with system information")

if __name__ == "__main__":
    test_pdf_export()
