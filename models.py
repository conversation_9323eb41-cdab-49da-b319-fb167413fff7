import sqlite3
import hashlib
from datetime import datetime, date
from config import Config

class DatabaseManager:
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Students table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                room_no TEXT NOT NULL,
                branch TEXT NOT NULL,
                year INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Attendance table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                date DATE NOT NULL,
                check_in_time TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (student_id) REFERENCES students (id),
                UNIQUE(student_id, date)
            )
        ''')
        
        # QR codes table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qr_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE UNIQUE NOT NULL,
                code_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_student(self, name, email, room_no, branch, year):
        """Add a new student"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO students (name, email, room_no, branch, year)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, email, room_no, branch, year))
            
            conn.commit()
            student_id = cursor.lastrowid
            conn.close()
            return student_id
        except sqlite3.IntegrityError:
            return None  # Email already exists
    
    def get_student_by_email(self, email):
        """Get student by email"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, email, room_no, branch, year
            FROM students WHERE email = ?
        ''', (email,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'name': result[1],
                'email': result[2],
                'room_no': result[3],
                'branch': result[4],
                'year': result[5]
            }
        return None
    
    def get_all_students(self):
        """Get all students"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, email, room_no, branch, year
            FROM students ORDER BY name
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        students = []
        for row in results:
            students.append({
                'id': row[0],
                'name': row[1],
                'email': row[2],
                'room_no': row[3],
                'branch': row[4],
                'year': row[5]
            })
        
        return students
    
    def delete_student(self, student_id):
        """Delete a student"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Delete attendance records first
        cursor.execute('DELETE FROM attendance WHERE student_id = ?', (student_id,))
        # Delete student
        cursor.execute('DELETE FROM students WHERE id = ?', (student_id,))
        
        conn.commit()
        affected_rows = cursor.rowcount
        conn.close()
        
        return affected_rows > 0
    
    def mark_attendance(self, student_id, attendance_date=None):
        """Mark attendance for a student"""
        if attendance_date is None:
            attendance_date = date.today()
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO attendance (student_id, date)
                VALUES (?, ?)
            ''', (student_id, attendance_date))
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False  # Already marked for today
    
    def get_attendance_streak(self, student_id):
        """Get current attendance streak for a student"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT date FROM attendance 
            WHERE student_id = ? 
            ORDER BY date DESC
        ''', (student_id,))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not dates:
            return 0
        
        streak = 0
        current_date = date.today()
        
        for attendance_date in dates:
            attendance_date = datetime.strptime(attendance_date, '%Y-%m-%d').date()
            if attendance_date == current_date:
                streak += 1
                current_date = current_date.replace(day=current_date.day - 1)
            else:
                break
        
        return streak
    
    def is_present_today(self, student_id):
        """Check if student is already present today"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        today = date.today()
        cursor.execute('''
            SELECT id FROM attendance
            WHERE student_id = ? AND date = ?
        ''', (student_id, today))

        result = cursor.fetchone()
        conn.close()

        return result is not None

    def validate_student_credentials(self, full_name, password):
        """Validate student credentials (full name and email as password)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Password is the email address
        cursor.execute('''
            SELECT id, name, email, room_no, branch, year
            FROM students WHERE LOWER(name) = LOWER(?) AND LOWER(email) = LOWER(?)
        ''', (full_name.strip(), password.strip()))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                'id': result[0],
                'name': result[1],
                'email': result[2],
                'room_no': result[3],
                'branch': result[4],
                'year': result[5]
            }
        return None

    def get_today_attendance(self):
        """Get today's attendance list with student details"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        today = date.today()
        cursor.execute('''
            SELECT s.id, s.name, s.email, s.room_no, s.branch, s.year, a.check_in_time
            FROM students s
            JOIN attendance a ON s.id = a.student_id
            WHERE a.date = ?
            ORDER BY a.check_in_time ASC
        ''', (today,))

        results = cursor.fetchall()
        conn.close()

        attendance_list = []
        for row in results:
            attendance_list.append({
                'id': row[0],
                'name': row[1],
                'email': row[2],
                'room_no': row[3],
                'branch': row[4],
                'year': row[5],
                'check_in_time': row[6]
            })

        return attendance_list

    def reset_today_attendance(self):
        """Reset all attendance records for today (Admin function)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        today = date.today()

        # Count records before deletion
        cursor.execute('''
            SELECT COUNT(*) FROM attendance WHERE date = ?
        ''', (today,))
        count = cursor.fetchone()[0]

        # Delete all attendance records for today
        cursor.execute('''
            DELETE FROM attendance WHERE date = ?
        ''', (today,))

        conn.commit()
        conn.close()

        return count

    def remove_student_attendance_today(self, student_id):
        """Remove specific student's attendance for today (Admin function)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        today = date.today()

        # Get student name before deletion
        cursor.execute('''
            SELECT s.name FROM students s
            JOIN attendance a ON s.id = a.student_id
            WHERE a.student_id = ? AND a.date = ?
        ''', (student_id, today))

        result = cursor.fetchone()
        student_name = result[0] if result else None

        if student_name:
            # Delete the attendance record
            cursor.execute('''
                DELETE FROM attendance
                WHERE student_id = ? AND date = ?
            ''', (student_id, today))

            conn.commit()

        conn.close()

        return student_name
