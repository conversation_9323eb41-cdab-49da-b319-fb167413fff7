from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import random
from datetime import date, datetime
from models import DatabaseManager
from qr_generator import QRCodeManager
from config import Config

app = Flask(__name__)
app.secret_key = Config.SECRET_KEY
app.config.from_object(Config)

# Initialize managers
db_manager = DatabaseManager()
qr_manager = QRCodeManager()

@app.route('/')
def index():
    """Main dashboard"""
    # Generate today's QR code
    qr_manager.generate_daily_qr_code()

    # Get random welcome message
    welcome_msg = random.choice(Config.WELCOME_MESSAGES)

    # Get today's QR code URL
    qr_url = qr_manager.get_today_qr_url()

    # Get total students count
    students = db_manager.get_all_students()
    total_students = len(students)

    # Get today's attendance
    today_attendance = db_manager.get_today_attendance()
    attendance_count = len(today_attendance)
    attendance_percentage = round((attendance_count / total_students * 100) if total_students > 0 else 0, 1)

    # Get QR scan URL for today (persistent)
    qr_scan_url = qr_manager.get_today_scan_url()

    # Get complete QR code information
    qr_info = qr_manager.get_qr_code_info()

    return render_template('index.html',
                         welcome_message=welcome_msg,
                         qr_url=qr_url,
                         qr_scan_url=qr_scan_url,
                         qr_info=qr_info,
                         total_students=total_students,
                         today_attendance=today_attendance,
                         attendance_count=attendance_count,
                         attendance_percentage=attendance_percentage,
                         today_date=date.today().strftime('%B %d, %Y'))

@app.route('/admin')
def admin():
    """Admin panel for user management"""
    students = db_manager.get_all_students()
    return render_template('admin.html', students=students)

@app.route('/add_student', methods=['POST'])
def add_student():
    """Add a new student"""
    try:
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip().lower()
        room_no = request.form.get('room_no', '').strip()
        branch = request.form.get('branch', '').strip()
        year = int(request.form.get('year', 0))
        
        # Validation
        if not all([name, email, room_no, branch, year]):
            flash('All fields are required! 📝', 'error')
            return redirect(url_for('admin'))
        
        if year < 1 or year > 4:
            flash('Year must be between 1 and 4! 📚', 'error')
            return redirect(url_for('admin'))
        
        # Add student
        student_id = db_manager.add_student(name, email, room_no, branch, year)
        
        if student_id:
            flash(f'🎉 Welcome {name}! Student added successfully! ✨', 'success')
        else:
            flash('😅 Oops! Email already exists. Try a different one!', 'error')
            
    except ValueError:
        flash('Invalid year format! Please enter a number 🔢', 'error')
    except Exception as e:
        flash(f'Something went wrong! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/delete_student/<int:student_id>', methods=['POST'])
def delete_student(student_id):
    """Delete a student"""
    try:
        if db_manager.delete_student(student_id):
            flash('👋 Student removed successfully! Hope they had fun! 🎈', 'success')
        else:
            flash('🤔 Student not found or already removed!', 'error')
    except Exception as e:
        flash(f'Error removing student! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/attendance')
def attendance_page():
    """QR code scanning page"""
    qr_url = qr_manager.get_today_qr_url()
    return render_template('attendance.html', qr_url=qr_url)

@app.route('/scan_attendance')
def scan_attendance():
    """QR code scan landing page"""
    qr_date = request.args.get('date', '')
    qr_code = request.args.get('code', '')

    if not qr_date or not qr_code:
        flash('Invalid QR code! Please scan a valid code! 🚫', 'error')
        return redirect(url_for('index'))

    # Validate QR code
    if not qr_manager.validate_qr_code(qr_date, qr_code):
        flash('QR code expired or invalid! Please get today\'s fresh code! ⏰', 'error')
        return redirect(url_for('index'))

    # Format date for display
    from datetime import datetime
    try:
        qr_date_formatted = datetime.strptime(qr_date, '%Y-%m-%d').strftime('%B %d, %Y')
    except:
        qr_date_formatted = qr_date

    return render_template('scan_attendance.html',
                         qr_date=qr_date,
                         qr_code=qr_code,
                         qr_date_formatted=qr_date_formatted)

@app.route('/mark_attendance_scan', methods=['POST'])
def mark_attendance_scan():
    """Handle attendance marking from QR scan"""
    try:
        full_name = request.form.get('full_name', '').strip()
        password = request.form.get('password', '').strip().lower()
        qr_date = request.form.get('qr_date', '').strip()
        qr_code = request.form.get('qr_code', '').strip()

        if not all([full_name, password, qr_date, qr_code]):
            return jsonify({
                'success': False,
                'message': 'Please fill in all fields! 📝'
            })

        # Validate QR code
        if not qr_manager.validate_qr_code(qr_date, qr_code):
            return jsonify({
                'success': False,
                'message': 'QR code expired or invalid! 🚫 Please scan today\'s fresh code!'
            })

        # Validate student credentials
        student = db_manager.validate_student_credentials(full_name, password)
        if not student:
            return jsonify({
                'success': False,
                'message': 'Invalid credentials! 🚫 Please check your name and email address and try again!'
            })

        # Check if already present today
        if db_manager.is_present_today(student['id']):
            return jsonify({
                'success': False,
                'message': f'Hey {student["name"]}! 👋 You\'re already checked in today! 🎯'
            })

        # Mark attendance
        if db_manager.mark_attendance(student['id']):
            # Get streak
            streak = db_manager.get_attendance_streak(student['id'])

            # Get random success message
            success_msg = random.choice(Config.SUCCESS_MESSAGES)

            # Add streak message if applicable
            if streak > 1:
                streak_msg = random.choice(Config.STREAK_MESSAGES).format(days=streak)
                success_msg += f" {streak_msg}"

            return jsonify({
                'success': True,
                'message': success_msg,
                'student_name': student['name'],
                'room_no': student['room_no'],
                'branch': student['branch'],
                'streak': streak
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to mark attendance! 😅 Try again!'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': random.choice(Config.ERROR_MESSAGES)
        })

@app.route('/check_in', methods=['POST'])
def check_in():
    """Handle attendance check-in (legacy method)"""
    try:
        email = request.form.get('email', '').strip().lower()
        qr_content = request.form.get('qr_content', '').strip()

        if not email:
            return jsonify({
                'success': False,
                'message': 'Please enter your email! 📧'
            })

        # For legacy QR codes, try to parse the old format
        try:
            parts = qr_content.split('_')
            if len(parts) >= 4 and parts[0] == 'HOSTEL' and parts[1] == 'ATTENDANCE':
                qr_date_str = parts[2]
                qr_hash = parts[3]

                # Validate using new method
                if not qr_manager.validate_qr_code(qr_date_str, qr_hash):
                    return jsonify({
                        'success': False,
                        'message': 'Invalid or expired QR code! 🚫 Get today\'s fresh code!'
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Invalid QR code format! 🚫 Please scan a valid code!'
                })
        except:
            return jsonify({
                'success': False,
                'message': 'Invalid QR code! 🚫 Please scan today\'s fresh code!'
            })

        # Find student
        student = db_manager.get_student_by_email(email)
        if not student:
            return jsonify({
                'success': False,
                'message': 'Email not found! 🔍 Are you registered?'
            })

        # Check if already present today
        if db_manager.is_present_today(student['id']):
            return jsonify({
                'success': False,
                'message': f'Hey {student["name"]}! 👋 You\'re already checked in today! 🎯'
            })

        # Mark attendance
        if db_manager.mark_attendance(student['id']):
            # Get streak
            streak = db_manager.get_attendance_streak(student['id'])

            # Get random success message
            success_msg = random.choice(Config.SUCCESS_MESSAGES)

            # Add streak message if applicable
            if streak > 1:
                streak_msg = random.choice(Config.STREAK_MESSAGES).format(days=streak)
                success_msg += f" {streak_msg}"

            return jsonify({
                'success': True,
                'message': success_msg,
                'student_name': student['name'],
                'room_no': student['room_no'],
                'streak': streak
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to mark attendance! 😅 Try again!'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': random.choice(Config.ERROR_MESSAGES)
        })

@app.route('/success')
def success_page():
    """Success page after check-in"""
    return render_template('success.html')

@app.route('/api/validate_qr', methods=['POST'])
def validate_qr():
    """API endpoint to validate QR code"""
    try:
        data = request.get_json()
        qr_content = data.get('qr_content', '')

        is_valid = qr_manager.validate_qr_code(qr_content)

        return jsonify({
            'valid': is_valid,
            'message': 'QR code is valid! 🎯' if is_valid else 'Invalid QR code! 🚫'
        })

    except Exception:
        return jsonify({
            'valid': False,
            'message': 'Error validating QR code! 😅'
        })

@app.route('/qr_info')
def qr_info():
    """QR code information and testing endpoint"""
    qr_info = qr_manager.get_qr_code_info()
    qr_scan_url = qr_manager.get_today_scan_url()

    return jsonify({
        'qr_info': qr_info,
        'scan_url': qr_scan_url,
        'current_time': datetime.now().isoformat(),
        'today': date.today().isoformat()
    })

@app.route('/regenerate_qr')
def regenerate_qr():
    """Force regenerate today's QR code (for testing)"""
    try:
        qr_manager.generate_daily_qr_code(force_regenerate=True)
        flash('🔄 QR code regenerated successfully! New code is now active! ✨', 'success')
    except Exception as e:
        flash(f'❌ Error regenerating QR code: {str(e)}', 'error')

    return redirect(url_for('index'))

@app.route('/admin/reset_attendance', methods=['POST'])
def reset_today_attendance():
    """Reset all attendance for today (Admin function)"""
    try:
        count = db_manager.reset_today_attendance()
        if count > 0:
            flash(f'🗑️ Successfully reset today\'s attendance! Removed {count} records. 📊', 'success')
        else:
            flash('ℹ️ No attendance records found for today to reset. 📅', 'info')
    except Exception as e:
        flash(f'❌ Error resetting attendance: {str(e)}', 'error')

    return redirect(url_for('index'))

@app.route('/admin/remove_attendance/<int:student_id>', methods=['POST'])
def remove_student_attendance(student_id):
    """Remove specific student's attendance for today (Admin function)"""
    try:
        student_name = db_manager.remove_student_attendance_today(student_id)
        if student_name:
            flash(f'✅ Removed {student_name} from today\'s attendance! 👋', 'success')
        else:
            flash('⚠️ Student attendance record not found for today! 🔍', 'warning')
    except Exception as e:
        flash(f'❌ Error removing attendance: {str(e)}', 'error')

    return redirect(url_for('index'))

if __name__ == '__main__':
    # Clean up old QR codes on startup
    qr_manager.cleanup_old_qr_codes()
    
    print("🚀 Starting Hostel Attendance System...")
    print("🎉 Ready to track those hostel vibes!")
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
