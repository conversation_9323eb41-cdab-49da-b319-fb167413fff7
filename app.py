from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import random
from datetime import date
from models import DatabaseManager
from qr_generator import QRCodeManager
from config import Config

app = Flask(__name__)
app.secret_key = Config.SECRET_KEY
app.config.from_object(Config)

# Initialize managers
db_manager = DatabaseManager()
qr_manager = QRCodeManager()

@app.route('/')
def index():
    """Main dashboard"""
    # Generate today's QR code
    qr_manager.generate_daily_qr_code()
    
    # Get random welcome message
    welcome_msg = random.choice(Config.WELCOME_MESSAGES)
    
    # Get today's QR code URL
    qr_url = qr_manager.get_today_qr_url()
    
    # Get total students count
    students = db_manager.get_all_students()
    total_students = len(students)
    
    return render_template('index.html', 
                         welcome_message=welcome_msg,
                         qr_url=qr_url,
                         total_students=total_students,
                         today_date=date.today().strftime('%B %d, %Y'))

@app.route('/admin')
def admin():
    """Admin panel for user management"""
    students = db_manager.get_all_students()
    return render_template('admin.html', students=students)

@app.route('/add_student', methods=['POST'])
def add_student():
    """Add a new student"""
    try:
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip().lower()
        room_no = request.form.get('room_no', '').strip()
        branch = request.form.get('branch', '').strip()
        year = int(request.form.get('year', 0))
        
        # Validation
        if not all([name, email, room_no, branch, year]):
            flash('All fields are required! 📝', 'error')
            return redirect(url_for('admin'))
        
        if year < 1 or year > 4:
            flash('Year must be between 1 and 4! 📚', 'error')
            return redirect(url_for('admin'))
        
        # Add student
        student_id = db_manager.add_student(name, email, room_no, branch, year)
        
        if student_id:
            flash(f'🎉 Welcome {name}! Student added successfully! ✨', 'success')
        else:
            flash('😅 Oops! Email already exists. Try a different one!', 'error')
            
    except ValueError:
        flash('Invalid year format! Please enter a number 🔢', 'error')
    except Exception as e:
        flash(f'Something went wrong! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/delete_student/<int:student_id>', methods=['POST'])
def delete_student(student_id):
    """Delete a student"""
    try:
        if db_manager.delete_student(student_id):
            flash('👋 Student removed successfully! Hope they had fun! 🎈', 'success')
        else:
            flash('🤔 Student not found or already removed!', 'error')
    except Exception as e:
        flash(f'Error removing student! 😅 {str(e)}', 'error')
    
    return redirect(url_for('admin'))

@app.route('/attendance')
def attendance_page():
    """QR code scanning page"""
    qr_url = qr_manager.get_today_qr_url()
    return render_template('attendance.html', qr_url=qr_url)

@app.route('/check_in', methods=['POST'])
def check_in():
    """Handle attendance check-in"""
    try:
        email = request.form.get('email', '').strip().lower()
        qr_content = request.form.get('qr_content', '').strip()
        
        if not email:
            return jsonify({
                'success': False,
                'message': 'Please enter your email! 📧'
            })
        
        # Validate QR code
        if not qr_manager.validate_qr_code(qr_content):
            return jsonify({
                'success': False,
                'message': 'Invalid or expired QR code! 🚫 Get today\'s fresh code!'
            })
        
        # Find student
        student = db_manager.get_student_by_email(email)
        if not student:
            return jsonify({
                'success': False,
                'message': 'Email not found! 🔍 Are you registered?'
            })
        
        # Check if already present today
        if db_manager.is_present_today(student['id']):
            return jsonify({
                'success': False,
                'message': f'Hey {student["name"]}! 👋 You\'re already checked in today! 🎯'
            })
        
        # Mark attendance
        if db_manager.mark_attendance(student['id']):
            # Get streak
            streak = db_manager.get_attendance_streak(student['id'])
            
            # Get random success message
            success_msg = random.choice(Config.SUCCESS_MESSAGES)
            
            # Add streak message if applicable
            if streak > 1:
                streak_msg = random.choice(Config.STREAK_MESSAGES).format(days=streak)
                success_msg += f" {streak_msg}"
            
            return jsonify({
                'success': True,
                'message': success_msg,
                'student_name': student['name'],
                'room_no': student['room_no'],
                'streak': streak
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to mark attendance! 😅 Try again!'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': random.choice(Config.ERROR_MESSAGES)
        })

@app.route('/success')
def success_page():
    """Success page after check-in"""
    return render_template('success.html')

@app.route('/api/validate_qr', methods=['POST'])
def validate_qr():
    """API endpoint to validate QR code"""
    try:
        data = request.get_json()
        qr_content = data.get('qr_content', '')
        
        is_valid = qr_manager.validate_qr_code(qr_content)
        
        return jsonify({
            'valid': is_valid,
            'message': 'QR code is valid! 🎯' if is_valid else 'Invalid QR code! 🚫'
        })
        
    except Exception:
        return jsonify({
            'valid': False,
            'message': 'Error validating QR code! 😅'
        })

if __name__ == '__main__':
    # Clean up old QR codes on startup
    qr_manager.cleanup_old_qr_codes()
    
    print("🚀 Starting Hostel Attendance System...")
    print("🎉 Ready to track those hostel vibes!")
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
