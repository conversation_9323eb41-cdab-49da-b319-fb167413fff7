/* Custom CSS for Hostel Attendance System */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

/* Gradient backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #6f42c1) !important;
}

/* QR Code styling */
.qr-code-img {
    max-width: 200px;
    border: 3px solid #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.qr-code-img:hover {
    transform: scale(1.05);
}

.qr-container {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-radius: 15px;
    display: inline-block;
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* Form controls */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background: var(--dark-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table td {
    border-color: #e9ecef;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading spinner */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .qr-code-img {
        max-width: 150px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

/* Fun hover effects */
.fas:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Status indicators */
.badge {
    border-radius: 20px;
    font-size: 0.8rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Success animation */
@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

.fa-check-circle {
    animation: bounce 1s ease-in-out;
}

/* Gradient text */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Card hover glow effect */
.card-glow:hover {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
}

/* Pulse animation for important elements */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Admin Actions Styling */
.admin-actions {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.admin-button {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.admin-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.student-card-admin {
    position: relative;
    transition: all 0.3s ease;
}

.student-card-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.student-remove-btn {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.student-remove-btn:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Admin warning colors */
.admin-warning {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.admin-warning:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
}

/* PDF Export Button Styling */
.btn-success.admin-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

.btn-success.admin-button:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-success.admin-button.disabled {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Admin actions button group */
.admin-actions .d-flex.gap-2 {
    gap: 0.5rem !important;
}

/* Responsive admin actions */
@media (max-width: 768px) {
    .admin-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .admin-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }

    .admin-actions .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .admin-actions button,
    .admin-actions a {
        width: 100%;
        margin-top: 0.25rem;
    }

    .student-remove-btn {
        opacity: 1; /* Always visible on mobile */
    }
}
