{% extends "base.html" %}

{% block title %}Mark Attendance - Hostel Attendance 🏠{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card card-glow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="card-title mb-0">
                    <i class="fas fa-qrcode"></i> QR Code Scanned Successfully! 🎯
                </h4>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-4x text-success pulse"></i>
                    </div>
                    <h5 class="text-success">Ready to mark your attendance! 🚀</h5>
                    <p class="text-muted">Please enter your details below to confirm your presence! ✨</p>
                </div>

                <!-- Attendance Form -->
                <form id="attendanceForm">
                    <div class="mb-4">
                        <label for="fullName" class="form-label">
                            <i class="fas fa-user"></i> Full Name 📝
                        </label>
                        <input type="text" class="form-control form-control-lg" 
                               id="fullName" name="full_name" required 
                               placeholder="Enter your full name exactly as registered..."
                               autocomplete="name">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> Enter your name exactly as it appears in the system
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password 🔐
                        </label>
                        <input type="email" class="form-control form-control-lg" 
                               id="password" name="password" required 
                               placeholder="Enter your email address..."
                               autocomplete="email">
                        <div class="form-text">
                            <i class="fas fa-key"></i> Your password is your registered email address
                        </div>
                    </div>

                    <!-- Hidden fields for QR validation -->
                    <input type="hidden" id="qrDate" name="qr_date" value="{{ qr_date }}">
                    <input type="hidden" id="qrCode" name="qr_code" value="{{ qr_code }}">

                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-success btn-lg pulse" id="markPresentBtn">
                            <i class="fas fa-check-circle"></i> Mark Me as Present! 🎉
                        </button>
                    </div>
                </form>

                <!-- Status Messages -->
                <div id="statusMessage" class="mt-3" style="display: none;"></div>

                <!-- QR Code Info -->
                <div class="card bg-light mt-4">
                    <div class="card-body text-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt"></i> QR Code Date: {{ qr_date_formatted }}
                            <br>
                            <i class="fas fa-clock"></i> Valid for today only! ⏰
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instructions Card -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-6 col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> How to Mark Attendance 📋
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="fas fa-user-check"></i> Step 1: Full Name
                        </h6>
                        <p class="small">Enter your complete name exactly as registered in the system</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="fas fa-envelope"></i> Step 2: Email Password
                        </h6>
                        <p class="small">Use your registered email address as the password</p>
                    </div>
                </div>
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Important:</strong> Both name and email must match exactly with your registration! 🎯
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Attendance Marked! 🎉
                </h5>
            </div>
            <div class="modal-body text-center">
                <div id="successContent">
                    <!-- Success message will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="window.close()">
                    Awesome! Close Window 😎
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> Oops! Something's not right 😅
                </h5>
            </div>
            <div class="modal-body text-center">
                <div id="errorContent">
                    <!-- Error message will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                    Try Again 🔄
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Handle form submission
document.getElementById('attendanceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fullName = document.getElementById('fullName').value.trim();
    const password = document.getElementById('password').value.trim();
    const qrDate = document.getElementById('qrDate').value;
    const qrCode = document.getElementById('qrCode').value;
    
    if (!fullName || !password) {
        showError('Please fill in all fields! 📝');
        return;
    }
    
    // Show loading state
    const btn = document.getElementById('markPresentBtn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Marking your attendance... ⏳';
    btn.disabled = true;
    
    // Submit attendance
    fetch('/mark_attendance_scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `full_name=${encodeURIComponent(fullName)}&password=${encodeURIComponent(password)}&qr_date=${encodeURIComponent(qrDate)}&qr_code=${encodeURIComponent(qrCode)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessModal(data);
        } else {
            showErrorModal(data.message);
        }
    })
    .catch(error => {
        showErrorModal('Something went wrong! Please try again! 😅');
    })
    .finally(() => {
        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
});

function showError(message) {
    const statusDiv = document.getElementById('statusMessage');
    statusDiv.className = 'alert alert-danger alert-dismissible fade show';
    statusDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    statusDiv.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 5000);
}

function showSuccessModal(data) {
    const successContent = document.getElementById('successContent');
    successContent.innerHTML = `
        <div class="mb-3">
            <i class="fas fa-user-check fa-4x text-success mb-3"></i>
            <h4>Welcome, ${data.student_name}! 👋</h4>
        </div>
        <div class="alert alert-success">
            ${data.message}
        </div>
        <div class="row text-center">
            <div class="col-4">
                <h6 class="text-primary">${data.room_no}</h6>
                <small class="text-muted">Room 🏠</small>
            </div>
            <div class="col-4">
                <h6 class="text-warning">${data.streak}</h6>
                <small class="text-muted">Streak 🔥</small>
            </div>
            <div class="col-4">
                <h6 class="text-info">${data.branch}</h6>
                <small class="text-muted">Branch 🎓</small>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();
    
    // Add confetti effect for streaks
    if (data.streak >= 3) {
        createConfetti();
    }
}

function showErrorModal(message) {
    const errorContent = document.getElementById('errorContent');
    errorContent.innerHTML = `
        <div class="mb-3">
            <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
        </div>
        <div class="alert alert-danger">
            ${message}
        </div>
        <div class="mt-3">
            <h6 class="text-muted">Double-check your details:</h6>
            <ul class="text-start">
                <li>Full name matches exactly with registration</li>
                <li>Email address is correct</li>
                <li>You're using today's QR code</li>
            </ul>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('errorModal'));
    modal.show();
}

function createConfetti() {
    const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
    
    for (let i = 0; i < 30; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.cssText = `
                position: fixed;
                top: -10px;
                left: ${Math.random() * 100}%;
                width: 8px;
                height: 8px;
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                z-index: 9999;
                animation: confettiFall 3s linear forwards;
                pointer-events: none;
                border-radius: 50%;
            `;
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 100);
    }
}

// Auto-focus full name input
document.getElementById('fullName').focus();

// Add real-time validation
document.getElementById('fullName').addEventListener('input', function() {
    if (this.value.trim().length > 0) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(this.value.trim())) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});
</script>
{% endblock %}
