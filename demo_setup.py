#!/usr/bin/env python3
"""
Demo setup script for Hostel Attendance System
This script adds some sample students for testing purposes
"""

from models import DatabaseManager
import random

def setup_demo_data():
    """Add sample students for demo purposes"""
    print("🚀 Setting up demo data for Hostel Attendance System...")
    
    db = DatabaseManager()
    
    # Sample students data
    sample_students = [
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'room_no': 'A-101',
            'branch': 'Computer Science',
            'year': 2
        },
        {
            'name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'room_no': 'B-205',
            'branch': 'Electronics',
            'year': 3
        },
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'room_no': 'C-301',
            'branch': 'Mechanical',
            'year': 1
        },
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'room_no': 'A-150',
            'branch': 'Civil Engineering',
            'year': 4
        },
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'room_no': 'B-120',
            'branch': 'Computer Science',
            'year': 2
        }
    ]
    
    print("📝 Adding sample students...")
    added_count = 0
    
    for student in sample_students:
        student_id = db.add_student(
            student['name'],
            student['email'],
            student['room_no'],
            student['branch'],
            student['year']
        )
        
        if student_id:
            print(f"✅ Added: {student['name']} ({student['email']})")
            added_count += 1
        else:
            print(f"⚠️  Skipped: {student['name']} (email already exists)")
    
    print(f"\n🎉 Demo setup complete! Added {added_count} students.")
    print("📱 You can now test the system with these sample emails:")
    
    for student in sample_students:
        print(f"   • {student['email']}")
    
    print("\n🚀 Start the application with: python app.py")
    print("🌐 Then visit: http://localhost:5000")

if __name__ == "__main__":
    setup_demo_data()
