#!/usr/bin/env python3
"""
Admin Functions Test Script
Tests the new admin functionality for attendance management
"""

from models import DatabaseManager
from datetime import date

def test_admin_functions():
    """Test all admin functionality"""
    print("🔧 Testing Admin Functions...")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    # Test 1: Show current attendance
    print("\n1️⃣ Current Attendance Status:")
    attendance = db_manager.get_today_attendance()
    print(f"   📊 Total present today: {len(attendance)}")
    
    for student in attendance:
        print(f"   ✅ {student['name']} ({student['room_no']}) - {student['check_in_time']}")
    
    if len(attendance) == 0:
        print("   📝 No students present today - marking some attendance for testing...")
        
        # Mark attendance for testing
        test_students = [
            ('<PERSON>', '<EMAIL>'),
            ('<PERSON>', '<EMAIL>')
        ]
        
        for name, email in test_students:
            student = db_manager.validate_student_credentials(name, email)
            if student and not db_manager.is_present_today(student['id']):
                db_manager.mark_attendance(student['id'])
                print(f"   ✅ Marked attendance for {name}")
        
        # Refresh attendance list
        attendance = db_manager.get_today_attendance()
        print(f"   📊 Updated attendance count: {len(attendance)}")
    
    # Test 2: Test individual student removal
    if len(attendance) > 0:
        print(f"\n2️⃣ Testing Individual Student Removal:")
        first_student = attendance[0]
        student_name = first_student['name']
        student_id = first_student['id']
        
        print(f"   🎯 Testing removal of: {student_name}")
        
        # Test the removal function
        removed_name = db_manager.remove_student_attendance_today(student_id)
        
        if removed_name:
            print(f"   ✅ Successfully removed {removed_name} from today's attendance")
            
            # Check updated attendance
            updated_attendance = db_manager.get_today_attendance()
            print(f"   📊 Updated attendance count: {len(updated_attendance)}")
            
            # Re-add the student for further testing
            student = db_manager.get_student_by_email(first_student['email'])
            if student:
                db_manager.mark_attendance(student['id'])
                print(f"   🔄 Re-added {student_name} for further testing")
        else:
            print(f"   ❌ Failed to remove {student_name}")
    
    # Test 3: Test attendance reset
    print(f"\n3️⃣ Testing Attendance Reset:")
    current_count = len(db_manager.get_today_attendance())
    print(f"   📊 Current attendance count: {current_count}")
    
    if current_count > 0:
        # Test reset function
        reset_count = db_manager.reset_today_attendance()
        print(f"   🗑️ Reset function returned: {reset_count} records removed")
        
        # Verify reset
        after_reset = db_manager.get_today_attendance()
        print(f"   📊 Attendance count after reset: {len(after_reset)}")
        
        if len(after_reset) == 0:
            print(f"   ✅ Reset successful! All {reset_count} records removed")
        else:
            print(f"   ❌ Reset failed! {len(after_reset)} records still remain")
    else:
        print("   ⚠️ No attendance to reset")
    
    # Test 4: Restore some attendance for dashboard testing
    print(f"\n4️⃣ Restoring Test Attendance:")
    test_students = [
        ('Priya Sharma', '<EMAIL>'),
        ('Sarah Williams', '<EMAIL>'),
        ('Raj Patel', '<EMAIL>')
    ]
    
    restored_count = 0
    for name, email in test_students:
        student = db_manager.validate_student_credentials(name, email)
        if student and not db_manager.is_present_today(student['id']):
            db_manager.mark_attendance(student['id'])
            print(f"   ✅ Restored attendance for {name}")
            restored_count += 1
        elif student:
            print(f"   ℹ️ {name} already present")
        else:
            print(f"   ❌ {name} not found in database")
    
    final_attendance = db_manager.get_today_attendance()
    print(f"   📊 Final attendance count: {len(final_attendance)}")
    
    print("\n" + "=" * 50)
    print("🎯 Admin Functions Test Complete!")
    print("\n📱 Dashboard Features Available:")
    print("✅ Reset Today's Attendance button (with confirmation)")
    print("✅ Individual student remove buttons (with confirmation)")
    print("✅ Real-time attendance statistics")
    print("✅ Admin action styling and visual indicators")
    print("✅ Mobile-responsive design")
    
    print(f"\n🌐 Visit http://localhost:5000 to test the admin UI!")
    print(f"   - Look for the 'Admin Actions' section in Today's Attendance")
    print(f"   - Test the 'Reset Today's Attendance' button")
    print(f"   - Test individual 'X' remove buttons on student cards")

if __name__ == "__main__":
    test_admin_functions()
