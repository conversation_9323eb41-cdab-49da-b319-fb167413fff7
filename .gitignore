# 🏠 Hostel Attendance System - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Flask specific
instance/
.webassets-cache
flask_session/

# Database files (SQLite)
*.db
*.sqlite
*.sqlite3
hostel_attendance.db

# Generated QR codes and PDFs
static/qr_codes/*.png
static/qr_codes/*.jpg
static/qr_codes/*.jpeg
*.pdf
test_*.pdf
web_test_*.pdf
attendance_report_*.pdf

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# Operating System
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node modules (if any frontend tools are added)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Flask development
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt

# Testing
.pytest_cache/
.coverage
htmlcov/

# Documentation builds
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
